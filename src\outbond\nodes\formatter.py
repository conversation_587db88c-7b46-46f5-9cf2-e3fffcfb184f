"""Formatter node that provides formatting functionality based on extraction schema."""

import json
import time
from typing import Dict, Any, Optional

from langchain_core.messages import SystemMessage, HumanMessage
from langsmith import AsyncClient


from outbond.shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SearchPhase, SearchState
from outbond.utils import convert_simple_schema_to_json_schema


async def format_final_answer(
    final_answer: str, 
    extraction_schema: Optional[Dict[str, Any]],
    llm: Optional[ChatOpenRouterAI] = None
) -> str:
    """Format the final answer according to the extraction schema.
    
    Args:
        final_answer: The raw final answer text
        extraction_schema: The extraction schema defining output format
        llm: Optional LLM instance for formatting
        
    Returns:
        Formatted answer as string (JSON string for json format, text for text format)
    """
    if not extraction_schema or not final_answer:
        return final_answer or ""
    
    # Handle text format - return as is
    if extraction_schema.get("format") == "text":
        return final_answer
    
    # Handle JSON format
    if extraction_schema.get("format") == "json":
        try:
            # Convert simple schema to JSON schema for LLM understanding
            json_schema = convert_simple_schema_to_json_schema(extraction_schema)
            


            message = {"schema",json.dumps(json_schema, indent=2),"final_answer",final_answer}
            # Initialize LLM if not provided
            if llm is None:
                llm = create_llm()
            

            
            LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
            client = AsyncClient(api_key=LANGSMITH_API_KEY)
            prompt = await client.pull_prompt("format_final_answer", include_model=False)
            chain = prompt | llm
            response = await chain.ainvoke(message)
            formatted_content = str(response.content).strip()
            
            # Clean up potential markdown formatting
            if formatted_content.startswith('```json'):
                formatted_content = formatted_content.replace('```json', '').replace('```', '').strip()
            elif formatted_content.startswith('```'):
                formatted_content = formatted_content.replace('```', '').strip()
            
            # Validate that it's proper JSON
            try:
                json.loads(formatted_content)
                return formatted_content
            except json.JSONDecodeError:
                # If JSON is invalid, return the original answer
                print(f"Warning: Generated content is not valid JSON: {formatted_content}")
                return final_answer
                
        except Exception as e:
            print(f"Error formatting answer: {str(e)}")
            return final_answer
    
    # For unknown formats, return as is
    return final_answer


async def formatter_node(state: SearchState) -> SearchState:
    """Complete node that formats the final answer based on extraction schema.
    
    Args:
        state: The current SearchState
        
    Returns:
        Updated SearchState with formatted final answer and phase set to COMPLETE
    """
    try:
        # Format the final answer based on extraction schema
        formatted_answer = await format_final_answer(
            state.finalAnswer or "",
            state.extraction_schema
        )
    
        
        # Update state with formatted answer and completion
        return SearchState(
            finalAnswer=formatted_answer,
            followUpQuestions=state.followUpQuestions,
            phase=SearchPhase.FORMATTER,
        )
        
    except Exception as e:
        # If formatting fails, return original answer
        print(f"Error in formatter_node: {str(e)}")
        return SearchState(
            finalAnswer=state.finalAnswer,
            followUpQuestions=state.followUpQuestions,
            phase=SearchPhase.FORMATTER,
        )
        
   
