2025-07-15 15:18:31 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_151831.log, Level: DEBUG
2025-07-15 15:18:31 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 15:18:31 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 15:18:31 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 15:18:31 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:31.806905Z'}
2025-07-15 15:18:32 - langgraph_runtime_inmem.queue - INFO - queue:72 - {'event': 'Starting 1 background workers', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:32.197072Z'}
2025-07-15 15:18:32 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:32.199071Z'}
2025-07-15 15:18:32 - langgraph_api.auth.custom - INFO - get_auth_instance:61 - {'langgraph_auth': 'None', 'event': 'Getting auth instance: None', 'thread_name': 'MainThread', 'logger': 'langgraph_api.auth.custom', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:32.201144Z', 'path': '/assistants/{assistant_id}/graph', 'method': 'GET'}
2025-07-15 15:18:33 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:33.220509Z'}
2025-07-15 15:18:33 - langgraph_runtime_inmem.queue - INFO - queue:132 - {'run_ids': [], 'event': 'Swept runs', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:33.222510Z'}
2025-07-15 15:18:34 - langgraph_runtime_inmem.queue - INFO - queue:141 - {'event': 'Shutting down background workers', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:34.440041Z'}
2025-07-15 15:18:34 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:18:34.440041Z'}
2025-07-15 15:18:34 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 15:18:34 - httpcore.connection - DEBUG - atrace:87 - close.complete
