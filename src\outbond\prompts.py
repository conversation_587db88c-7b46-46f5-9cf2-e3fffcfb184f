"""Prompts for the outbound search agent."""

from datetime import datetime


def get_current_date_context() -> str:
    """Get current date context for prompts."""
    current_date = datetime.now()
    return f"Current date: {current_date.strftime('%B %d, %Y')}"


ANALYZE_QUERY_SYSTEM_PROMPT = """Analyze this search query and explain what you understand the user is looking for.

Instructions:
- Start with a clear, concise title (e.g., "Researching egg shortage" or "Understanding climate change impacts")
- Then explain in 1-2 sentences what aspects of the topic the user wants to know about
- If this relates to previous questions, acknowledge that connection
- Finally, mention that you'll search for information to help answer their question
- Only mention searching for "latest" information if the query is explicitly about recent events or current trends

Keep it natural and conversational, showing you truly understand their request.""" 